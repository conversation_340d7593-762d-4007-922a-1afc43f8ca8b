# Detailed Instructions for JSON Schema to TypeBox Conversion Process. 

## We are only converting services that contain a [serviceName].schema.ts file

## 🚨 CRITICAL RULE 🚨
**ALWAYS USE `server/schema-diff/service-schemas-with-common-indicators.json` AS THE SINGLE SOURCE OF TRUTH**
**<PERSON>VE<PERSON> LOOK AT EXISTING TYPEBOX SCHEMAS - THEY ARE INCORRECT - DON'T COMPARE THEM, DON'T WORRY ABOUT THE EXISTING SCHEMA, JUST REPLACE IT**

## DO NOT MAKE CHANGES TO ANY FILES BESIDES THE SCHEMA FILES

## Step-by-Step Process:

### Step 1: Extract the Exact JSON Schema
1. Open `server/schema-diff/service-schemas-with-common-indicators.json`
2. Find the service name (e.g., "cares", "bills", etc.)
3. Copy the ENTIRE JSON schema for that service from `"service-name": {` to the closing `}`
4. Save it to a temporary file `temp-[service-name]-schema.json`
5. **CRITICAL**: Include ALL properties exactly as they appear - do not modify anything

### Step 2: Handle $comment Fields
Before running the converter, find any fields with ONLY `$comment` properties and add `"type": "object"`:
```json
// BEFORE:
"fieldName": {
  "$comment": "***schemaName used here***"
}

// AFTER:
"fieldName": {
  "$comment": "***schemaName used here***",
  "type": "object"
}
```

### Step 3: Run TypeBox Conversion
Execute: `cd server && npx schema2typebox -i ../temp-[service-name]-schema.json --output-stdout`

### Step 4: Replace $comment References with Common Schemas
In the converted TypeBox code, replace `Type.Any()` fields that had `$comment` references:
- `***imageSchema used here***` → use `imageSchema`
- `***addressSchema used here***` → use `addressSchema`  
- `***taxSchema used here***` → use `taxSchema`
- Add the schema to imports if needed

### Step 5: Check Pattern Properties
1. Search `server/schema-diff/pattern-properties-usage-clean.json` for the service name
2. If pattern properties exist, convert `Type.Unknown()` to `Type.Record(Type.String(), [proper object structure])`

### Step 6: Convert Union Types to ObjectIdSchema
Replace all:
```typescript
Type.Union([
  Type.String({ objectid: true }),
  Type.Object({}, { additionalProperties: true }),
])
```
With: `ObjectIdSchema()`

### Step 7: Replace Type.Unknown() with Type.Any()
Change all `Type.Unknown()` to `Type.Any()` for consistency

### Step 8: Update Main Schema
1. Replace the entire main schema export with converted code - most files have // export const serviceSchema = ##ADD TYPEBOX SCHEMA YOU CONVERTED HERE

2. Ensure it includes `{ $id: 'ServiceName', additionalProperties: false }`
3. Keep `...commonFields` at the end

### Step 9: Update Patch Schema Pattern
**import `commonPatch` from the common-typebox-schemas.js file if needed**

Replace the patch schema section with:
```typescript
// Pick ObjectId fields and nested ObjectId fields for query properties
const [serviceName]QueryProperties = Type.Pick([serviceName]Schema, ['_id', 'field1', 'field2', ...])

export const [serviceName]PatchSchema = commonPatch([serviceName]Schema, {pushPullOpts: [], pickedForSet:[serviceName]QueryProperties})
```

Where:
- Include ALL ObjectId fields from the schema in the Pick list - the top-level keys only. If there is a nested ObjectId, include the top-level key. 

### Step 10: Clean Up
1. Remove temporary JSON file
2. Verify the schema compiles without errors

## Common Patterns:


### ObjectId Fields to Include in Pick:
- Always include `_id`
- Include any field that uses `ObjectIdSchema()`
- Include only top-level keys for nested ObjectId fields (like `mandate.fingerprint` - would include just 'mandage', `prices.threads` - would include just 'prices')

## Validation Checklist:
- [ ] Used exact JSON schema from source of truth file
- [ ] All properties from JSON schema are present in TypeBox schema
- [ ] $comment fields replaced with proper common schemas
- [ ] Pattern properties converted to Type.Record()
- [ ] Union types converted to ObjectIdSchema()
- [ ] Type.Unknown() changed to Type.Any()
- [ ] $id property matches service name
- [ ] commonFields included
- [ ] Patch schema uses commonPatch pattern
- [ ] All ObjectId fields included in Pick list
- [ ] Temporary file cleaned up

## Example Complete Conversion:
```typescript
export const serviceSchema = Type.Object({
  _id: ObjectIdSchema(),
  field1: Type.String(),
  field2: Type.Optional(ObjectIdSchema()),
  // ... all fields from JSON schema
  ...commonFields
}, { $id: 'ServiceName', additionalProperties: false })

// Pick ObjectId fields and nested ObjectId fields for query properties
const serviceQueryProperties = Type.Pick(serviceSchema, ['_id', 'field2', 'nestedField'])

export const servicePatchSchema = commonPatch(serviceSchema, { pushPullOpts: [], pickedForSet: serviceQueryProperties })
```

This process ensures 100% accuracy and consistency across all schema conversions.
