// TypeBox schema for bills service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, queryWrapper, addressSchema, imageSchema, taxSchema, rRuleSchema, commonPatch, phoneSchema } from '../../utils/common/typebox-schemas.js'

export const billsSchema = Type.Object({
  _id: ObjectIdSchema(),
  to: Type.Optional(ObjectIdSchema()),
  toName: Type.Optional(Type.String()),
  toModel: Type.Optional(Type.Union([Type.Literal("orgs"), Type.Literal("ppls")])),
  toEmail: Type.Optional(Type.String()),
  toAddress: Type.Optional(addressSchema),
  toPhone: Type.Optional(phoneSchema),
  from: Type.Optional(ObjectIdSchema()),
  fromName: Type.Optional(Type.String()),
  formModel: Type.Optional(Type.Union([Type.Literal("orgs"), Type.Literal("ppls")])),
  fromEmail: Type.Optional(Type.String()),
  fromAddress: Type.Optional(addressSchema),
  fromPhone: Type.Optional(phoneSchema),
  logo: Type.Optional(imageSchema),
  billDate: Type.Optional(Type.Any()),
  dueDate: Type.Optional(Type.Any()),
  currency: Type.Optional(Type.Union([Type.Literal("usd")])),
  lastTaxTotal: Type.Optional(Type.Number()),
  lineItems: Type.Optional(Type.Array(Type.Object({
    title: Type.Optional(Type.String()),
    description: Type.Optional(Type.String()),
    amount: Type.Optional(Type.Number()),
    code: Type.Optional(Type.String()),
    itemId: Type.Optional(ObjectIdSchema()),
    itemService: Type.Optional(Type.String()),
    qty: Type.Optional(Type.Number()),
    settings: Type.Optional(Type.Object({
      tax: taxSchema
    })),
  }))),
  threads: Type.Optional(Type.Array(ObjectIdSchema())),
  settings: Type.Optional(Type.Object({
    tax: taxSchema
  })),
  payments: Type.Optional(Type.Array(ObjectIdSchema())),
  recurrence: Type.Optional(rRuleSchema),
  paymentLink: Type.Optional(Type.String()),
  status: Type.Optional(Type.Union([
    Type.Literal("open"),
    Type.Literal("paid"),
    Type.Literal("closed"),
  ])),
  files: Type.Optional(
      Type.Record(
          Type.String(),
          imageSchema
      )
  ),
  ...commonFields
}, { $id: 'Bills', additionalProperties: false })

export type Bills = Static<typeof billsSchema>
export const billsValidator = getValidator(billsSchema, dataValidator)
export const billsResolver = resolve<Bills, HookContext>({})
export const billsExternalResolver = resolve<Bills, HookContext>({})

export const billsDataSchema = Type.Object({
  ...Type.Omit(billsSchema, ['_id']).properties
}, { additionalProperties: false })

export type BillsData = Static<typeof billsDataSchema>
export const billsDataValidator = getValidator(billsDataSchema, dataValidator)
export const billsDataResolver = resolve<BillsData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const billsQueryProperties = Type.Pick(billsSchema, ['_id', 'to', 'from', 'lineItems', 'threads', 'payments'], { additionalProperties: true })

export const billsPatchSchema = commonPatch(billsSchema, {pushPullOpts: [], pickedForSet:billsQueryProperties})
export type BillsPatch = Static<typeof billsPatchSchema>
export const billsPatchValidator = getValidator(billsPatchSchema, dataValidator)
export const billsPatchResolver = resolve<BillsPatch, HookContext>({})
export const billsQuerySchema = queryWrapper(billsQueryProperties)
export type BillsQuery = Static<typeof billsQuerySchema>
export const billsQueryValidator = getValidator(billsQuerySchema, queryValidator)
export const billsQueryResolver = resolve<BillsQuery, HookContext>({})
