// TypeBox schema for comps service
import {resolve} from '@feathersjs/schema'
import {Type, getValidator, ObjectIdSchema} from '@feathersjs/typebox'
import type {Static} from '@feathersjs/typebox'
import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {
    commonFields,
    queryWrapper,
    commonPatch,
    imageSchema,
    geoJsonSchema
} from '../../utils/common/typebox-schemas.js'
import { compsCamsSchema } from './schemas/comps-cams.js'
export const compsSchema = Type.Object({
    _id: ObjectIdSchema(),
    key: Type.String(),
    video: Type.Optional(imageSchema),
    references: Type.Optional(Type.Array(ObjectIdSchema())),
    ad: Type.Optional(Type.String()),
    geo: Type.Optional(geoJsonSchema),
    stages: Type.Optional(
        Type.Record(Type.String(), Type.Object({
            label: Type.Optional(Type.String()),
            color: Type.Optional(Type.String())
        }))
    ),
    contact: Type.Optional(
        Type.Object({
            name: Type.Optional(Type.String()),
            phone: Type.Optional(Type.String()),
            email: Type.Optional(Type.String())
        })
    ),
    access: Type.Optional(
        Type.Union([Type.Literal("public"), Type.Literal("private")])
    ),
    ...compsCamsSchema,
    ...commonFields
}, {$id: "Comps", additionalProperties: false})

import { setDefEstUnits } from './schemas/validators.js'


export type Comps = Static<typeof compsSchema>
export const compsValidator = getValidator(compsSchema, dataValidator)
export const compsResolver = resolve<Comps, HookContext>({
    properties: {
        estHours: setDefEstUnits
    }
})
export const compsExternalResolver = resolve<Comps, HookContext>({})

export const compsDataSchema = Type.Object({
    ...Type.Omit(compsSchema, ['_id']).properties
}, {additionalProperties: false})

export type CompsData = Static<typeof compsDataSchema>
export const compsDataValidator = getValidator(compsDataSchema, dataValidator)
export const compsDataResolver = resolve<CompsData, HookContext>({
    class: async (val) => {
        if (!val) return 'ee'
        return val
    }

})

// Pick ObjectId fields and nested ObjectId fields for query properties
const compsQueryProperties = Type.Pick(compsSchema, ['_id', 'references', 'org', 'contract', 'createdBy', 'updatedBy'])

export const compsPatchSchema = commonPatch(compsSchema, {pushPullOpts: [], pickedForSet: compsQueryProperties})
export type CompsPatch = Static<typeof compsPatchSchema>
export const compsPatchValidator = getValidator(compsPatchSchema, dataValidator)
export const compsPatchResolver = resolve<CompsPatch, HookContext>({})
export const compsQuerySchema = queryWrapper(Type.Object({
    ...compsQueryProperties.properties
}, {additionalProperties: true}))
export type CompsQuery = Static<typeof compsQuerySchema>
export const compsQueryValidator = getValidator(compsQuerySchema, queryValidator)
export const compsQueryResolver = resolve<CompsQuery, HookContext>({})
