import {ObjectIdSchema, Type} from '@feathersjs/typebox';

export const compsCamsSchema = {
    org: ObjectIdSchema(),
    contract: Type.Optional(ObjectIdSchema()),
    interval: Type.Union([
        Type.Literal("hour"),
        Type.Literal("day"),
        Type.Literal("week"),
        Type.Literal("month"),
        Type.Literal("quarter"),
        Type.Literal("year"),
        Type.Literal("once"),
    ]),
    estHours: Type.Optional(Type.Number()),
    amount: Type.Number(),
    terms: Type.Optional(Type.String()),
    name: Type.String(),
    class: Type.Optional(Type.Union([Type.Literal("ee"), Type.Literal("ic")])),
    extras: Type.Optional(
        Type.Record(Type.String(), Type.Object({
            due: Type.Optional(Type.String()),
            awarded: Type.Optional(Type.String()),
            off: Type.Optional(Type.Boolean()),
            banks: Type.Optional(Type.Boolean()),
            type: Type.Optional(Type.Union([
                Type.Literal("percent"),
                Type.Literal("flat"),
                Type.Literal("units")
            ])),
            unit: Type.Optional(Type.Union([
                Type.Literal("hour"),
                Type.Literal("day"),
                Type.Literal("week"),
                Type.Literal("month"),
                Type.Literal("quarter"),
                Type.Literal("year"),
                Type.Literal("once")
            ])),
            amount: Type.Optional(Type.Number()),
            interval: Type.Optional(Type.Union([
                Type.Literal("hour"),
                Type.Literal("day"),
                Type.Literal("week"),
                Type.Literal("month"),
                Type.Literal("quarter"),
                Type.Literal("year"),
                Type.Literal("once")
            ])),
            terms: Type.Optional(Type.String()),
            limit: Type.Optional(Type.Number())
        }))
    )
}
