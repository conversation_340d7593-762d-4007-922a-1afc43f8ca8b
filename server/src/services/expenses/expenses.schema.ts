// TypeBox schema for expenses service
import {resolve} from '@feathersjs/schema'
import {Type, getValidator, ObjectIdSchema} from '@feathersjs/typebox'
import type {Static} from '@feathersjs/typebox'
import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {commonFields, queryWrapper, commonPatch} from '../../utils/common/typebox-schemas.js'

const closedLimitSchema = Type.Object({
    id: Type.Optional(Type.String()),
    closedAt: Type.Optional(Type.Any()),
    memo: Type.Optional(Type.String())
})
export const expensesSchema = Type.Object({
    _id: ObjectIdSchema(),
    amount: Type.Optional(Type.Number()),
    max_transaction: Type.Optional(Type.Number()),
    recurs: Type.Optional(Type.Number()),
    recur_start: Type.Optional(Type.String()),
    next_reset: Type.Optional(Type.String()),
    lock_date: Type.Optional(Type.String()),
    spent: Type.Optional(Type.Number()),
    spent_pending: Type.Optional(Type.Number()),
    singleUse: Type.Optional(Type.Boolean()),
    singleVendor: Type.Optional(Type.Boolean()),
    perTransactionLimit: Type.Optional(Type.Number()),
    approvers: Type.Optional(Type.Array(ObjectIdSchema())),
    budget: ObjectIdSchema(),
    limit_owner: ObjectIdSchema(),
    closedLimitIds: Type.Optional(Type.Array(Type.String())),
    closedLimits: Type.Optional(Type.Array(closedLimitSchema)),
    last4: Type.Optional(Type.String()),
    lastSync: Type.Optional(Type.Any()),
    ramp_limit: Type.Optional(Type.String()),
    managers: Type.Optional(Type.Array(ObjectIdSchema())),
    ramp_whitelist: Type.Optional(Type.Array(Type.Number())),
    ramp_blacklist: Type.Optional(Type.Array(Type.Number())),
    vendor_whitelist: Type.Optional(Type.Array(ObjectIdSchema())),
    vendor_blacklist: Type.Optional(Type.Array(ObjectIdSchema())),
    mcc_whitelist: Type.Optional(Type.Array(Type.String())),
    mcc_blacklist: Type.Optional(Type.Array(Type.String())),
    members: Type.Optional(Type.Array(ObjectIdSchema())),
    name: Type.String(),
    owner: ObjectIdSchema(),
    preAuth: Type.Optional(Type.Number()),
    status: Type.Optional(Type.Union([
        Type.Literal("active"),
        Type.Literal("inactive"),
        Type.Literal("canceled"),
        Type.Literal("pending-verification")
    ])),
    users: Type.Optional(Type.Array(ObjectIdSchema())),
    ...commonFields
}, {$id: 'Expenses', additionalProperties: false})

export type Expenses = Static<typeof expensesSchema>
export const expensesValidator = getValidator(expensesSchema, dataValidator)
export const expensesResolver = resolve<Expenses, HookContext>({})
export const expensesExternalResolver = resolve<Expenses, HookContext>({})

export const expensesDataSchema = Type.Object({
    ...Type.Omit(expensesSchema, ['_id']).properties
}, {additionalProperties: false})

export type ExpensesData = Static<typeof expensesDataSchema>
export const expensesDataValidator = getValidator(expensesDataSchema, dataValidator)
export const expensesDataResolver = resolve<ExpensesData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const expensesQueryProperties = Type.Pick(expensesSchema, ['_id', 'budget', 'limit_owner', 'approvers', 'managers', 'vendor_whitelist', 'vendor_blacklist', 'members', 'owner', 'users', 'createdBy', 'updatedBy', 'env', 'host', 'ref', 'changeLog'])

export const expensesPatchSchema = commonPatch(expensesSchema, {
    pushPullOpts: [
        {path: 'mcc_whitelist', type: {type: 'string'}},
        {path: 'mcc_blacklist', type: {type: 'string'}},
        {path: 'closedLimits', type: closedLimitSchema},
        {path: 'closedLimitIds', type: {type: 'string'}}
    ],
    pickedForSet: expensesQueryProperties
})
export type ExpensesPatch = Static<typeof expensesPatchSchema>
export const expensesPatchValidator = getValidator(expensesPatchSchema, dataValidator)
export const expensesPatchResolver = resolve<ExpensesPatch, HookContext>({})

export const expensesQuerySchema = queryWrapper(expensesQueryProperties)
export type ExpensesQuery = Static<typeof expensesQuerySchema>
export const expensesQueryValidator = getValidator(expensesQuerySchema, queryValidator)
export const expensesQueryResolver = resolve<ExpensesQuery, HookContext>({})
