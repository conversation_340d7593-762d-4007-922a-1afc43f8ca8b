// TypeBox schema for fb-res service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, queryWrapper, commonPatch } from '../../utils/common/typebox-schemas.js'

export const fbResSchema = Type.Object({
  _id: ObjectIdSchema(),
  person: Type.Optional(ObjectIdSchema()),
  form: Type.Optional(ObjectIdSchema()),
  formData: Type.Optional(Type.Any()),
  lastField: Type.Optional(Type.String()),
  ...commonFields
}, { $id: 'FbRes', additionalProperties: false })

export type FbRes = Static<typeof fbResSchema>
export const fbResValidator = getValidator(fbResSchema, dataValidator)
export const fbResResolver = resolve<FbRes, HookContext>({})
export const fbResExternalResolver = resolve<FbRes, HookContext>({})

export const fbResDataSchema = Type.Object({
  ...Type.Omit(fbResSchema, ['_id']).properties
}, { additionalProperties: false })

export type FbResData = Static<typeof fbResDataSchema>
export const fbResDataValidator = getValidator(fbResDataSchema, dataValidator)
export const fbResDataResolver = resolve<FbResData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const fbResQueryProperties = Type.Pick(fbResSchema, ['_id', 'person', 'form'])

export const fbResPatchSchema = commonPatch(fbResSchema, { pushPullOpts: [], pickedForSet: fbResQueryProperties })
export type FbResPatch = Static<typeof fbResPatchSchema>
export const fbResPatchValidator = getValidator(fbResPatchSchema, dataValidator)
export const fbResPatchResolver = resolve<FbResPatch, HookContext>({})

// Allow querying on any field from the main schema
export const fbResQuerySchema = queryWrapper(fbResSchema)
export type FbResQuery = Static<typeof fbResQuerySchema>
export const fbResQueryValidator = getValidator(fbResQuerySchema, queryValidator)
export const fbResQueryResolver = resolve<FbResQuery, HookContext>({})
