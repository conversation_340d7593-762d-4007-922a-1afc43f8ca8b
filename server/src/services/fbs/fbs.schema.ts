// TypeBox schema for fbs service
import {resolve} from '@feathersjs/schema'
import {Type, getValidator, ObjectIdSchema} from '@feathersjs/typebox'
import type {Static} from '@feathersjs/typebox'
import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {
    commonFields,
    queryWrapper,
    commonPatch,
    imageSchema
} from '../../utils/common/typebox-schemas.js'

export const fbsSchema = Type.Object({
    _id: ObjectIdSchema(),
    active: Type.Optional(Type.Boolean()),
    owner: Type.Optional(ObjectIdSchema()),
    org: Type.Optional(ObjectIdSchema()),
    live: Type.Optional(Type.Boolean()),
    parent: Type.Optional(ObjectIdSchema()),
    children: Type.Optional(Type.Array(ObjectIdSchema())),
    name: Type.Optional(Type.String()),
    primaryColor: Type.Optional(Type.String()),
    secondaryColor: Type.Optional(Type.String()),
    description: Type.Optional(Type.String()),
    avatar: Type.Optional(imageSchema),
    welcomeTitle: Type.Optional(Type.String()),
    welcomeMessage: Type.Optional(Type.String()),
    welcomeImage: Type.Optional(imageSchema),
    welcomeVideos: Type.Optional(imageSchema),
    welcomeFiles: Type.Optional(imageSchema),
    finishTitle: Type.Optional(Type.String()),
    finishMessage: Type.Optional(Type.String()),
    finishImage: Type.Optional(imageSchema),
    finishVideos: Type.Optional(imageSchema),
    finishFiles: Type.Optional(imageSchema),
    dark: Type.Optional(Type.Boolean()),
    class: Type.Optional(Type.String()),
    products: Type.Optional(Type.Object({
        ids: Type.Optional(Type.Array(Type.Any()))
    })),
    style: Type.Optional(Type.Object({
        background: Type.Optional(Type.String()),
        color: Type.Optional(Type.String()),
        padding: Type.Optional(Type.String()),
    })),
    fields: Type.Optional(Type.Array(Type.Object({
        id: Type.Optional(Type.String()),
        color: Type.Optional(Type.String()),
        icon: Type.Optional(Type.String()),
        label: Type.Optional(Type.String()),
        value: Type.Optional(Type.Any()),
        next: Type.Optional(Type.Array(Type.Object({
            field: Type.Optional(Type.String()),
            if: Type.Optional(Type.Object({
                check: Type.Optional(Type.String()),
                arg: Type.Optional(Type.Any()),
                error: Type.Optional(Type.String()),
                rule: Type.Optional(Type.String()),
            })),
        }))),
        validators: Type.Optional(Type.Object({
            check: Type.Optional(Type.String()),
            arg: Type.Optional(Type.Any()),
            error: Type.Optional(Type.String()),
            rule: Type.Optional(Type.String()),
        })),
        slots: Type.Optional(Type.Array(Type.Any())),
        rules: Type.Optional(Type.Array(Type.Any())),
        errs: Type.Optional(Type.Array(Type.Any())),
        fieldType: Type.Optional(Type.String()),
        path: Type.Optional(Type.String()),
        attrs: Type.Optional(Type.Any()),
        "div-attrs": Type.Optional(Type.Any()),
    }))),
    responses: Type.Optional(Type.Array(ObjectIdSchema())),
    canEdit: Type.Optional(Type.Array(ObjectIdSchema())),
    ...commonFields
}, {$id: 'Fbs', additionalProperties: false})

export type Fbs = Static<typeof fbsSchema>
export const fbsValidator = getValidator(fbsSchema, dataValidator)
export const fbsResolver = resolve<Fbs, HookContext>({})
export const fbsExternalResolver = resolve<Fbs, HookContext>({})

export const fbsDataSchema = Type.Object({
    ...Type.Omit(fbsSchema, ['_id']).properties
}, {additionalProperties: false})

export type FbsData = Static<typeof fbsDataSchema>
export const fbsDataValidator = getValidator(fbsDataSchema, dataValidator)
export const fbsDataResolver = resolve<FbsData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const fbsQueryProperties = Type.Pick(fbsSchema, ['_id', 'owner', 'org', 'parent', 'children', 'responses', 'canEdit'])

export const fbsPatchSchema = commonPatch(fbsSchema, {
    pushPullOpts: [
        {path: 'children', type: ObjectIdSchema()},
        {path: 'responses', type: ObjectIdSchema()},
        {path: 'canEdit', type: ObjectIdSchema()},
        {path: 'welcomeVideos', type: imageSchema},
        {path: 'welcomeFiles', type: imageSchema},
        {path: 'finishVideos', type: imageSchema},
        {path: 'finishFiles', type: imageSchema},
    ],
    pickedForSet: fbsQueryProperties
})
export type FbsPatch = Static<typeof fbsPatchSchema>
export const fbsPatchValidator = getValidator(fbsPatchSchema, dataValidator)
export const fbsPatchResolver = resolve<FbsPatch, HookContext>({})

// Allow querying on any field from the main schema
export const fbsQuerySchema = queryWrapper(fbsSchema)
export type FbsQuery = Static<typeof fbsQuerySchema>
export const fbsQueryValidator = getValidator(fbsQuerySchema, queryValidator)
export const fbsQueryResolver = resolve<FbsQuery, HookContext>({})
