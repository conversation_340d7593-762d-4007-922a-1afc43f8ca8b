// TypeBox schema for fingerprints service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, queryWrapper, commonPatch } from '../../utils/common/typebox-schemas.js'

export const fingerprintsSchema = Type.Object({
  _id: ObjectIdSchema(),
  turnstile: Type.Optional(Type.Object({
    success: Type.Optional(Type.Boolean()),
    challenge_ts: Type.Optional(Type.String()),
    hostname: Type.Optional(Type.String()),
    "error-codes": Type.Optional(Type.Array(Type.Any())),
    action: Type.Optional(Type.String()),
    cdata: Type.Optional(Type.String()),
    metadata: Type.Optional(Type.Any()),
  }, { additionalProperties: true })),
  visitorId: Type.Optional(Type.String()),
  visits: Type.Optional(Type.Array(Type.Object({
    date: Type.Optional(Type.Any()),
    domain: Type.Optional(Type.String()),
    ipInfo: Type.Optional(Type.Object({
      ip: Type.Optional(Type.String()),
      range: Type.Optional(Type.Array(Type.Number())),
      country: Type.Optional(Type.String()),
      region: Type.Optional(Type.String()),
      eu: Type.Optional(Type.String()),
      timezone: Type.Optional(Type.String()),
      city: Type.Optional(Type.String()),
      lngLat: Type.Optional(Type.Array(Type.Number())),
      ll: Type.Optional(Type.Array(Type.Number())),
      metro: Type.Optional(Type.Number()),
      area: Type.Optional(Type.Number()),
    }, { additionalProperties: true })),
    incognito: Type.Optional(Type.Boolean()),
  }, { additionalProperties: true }))),
  name: Type.Optional(Type.String()),
  manufacturer: Type.Optional(Type.String()),
  product: Type.Optional(Type.String()),
  osName: Type.Optional(Type.String()),
  screenWidth: Type.Optional(Type.Any()),
  touch: Type.Optional(Type.Boolean()),
  incognito: Type.Optional(Type.Boolean()),
  type: Type.Optional(Type.String()),
  ua: Type.Optional(Type.String()),
  ipInfo: Type.Optional(Type.Object({
    ip: Type.Optional(Type.String()),
    range: Type.Optional(Type.Array(Type.Number())),
    country: Type.Optional(Type.String()),
    region: Type.Optional(Type.String()),
    eu: Type.Optional(Type.String()),
    timezone: Type.Optional(Type.String()),
    city: Type.Optional(Type.String()),
    lngLat: Type.Optional(Type.Array(Type.Number())),
    ll: Type.Optional(Type.Array(Type.Number())),
    metro: Type.Optional(Type.Number()),
    area: Type.Optional(Type.Number()),
  }, { additionalProperties: true })),
  ...commonFields
}, { $id: 'Fingerprints', additionalProperties: true })

export type Fingerprints = Static<typeof fingerprintsSchema>
export const fingerprintsValidator = getValidator(fingerprintsSchema, dataValidator)
export const fingerprintsResolver = resolve<Fingerprints, HookContext>({})
export const fingerprintsExternalResolver = resolve<Fingerprints, HookContext>({})

export const fingerprintsDataSchema = Type.Object({
  ...Type.Omit(fingerprintsSchema, ['_id']).properties
}, { additionalProperties: false })

export type FingerprintsData = Static<typeof fingerprintsDataSchema>
export const fingerprintsDataValidator = getValidator(fingerprintsDataSchema, dataValidator)
export const fingerprintsDataResolver = resolve<FingerprintsData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const fingerprintsQueryProperties = Type.Pick(fingerprintsSchema, ['_id'])

export const fingerprintsPatchSchema = commonPatch(fingerprintsSchema, { pushPullOpts: [], pickedForSet: fingerprintsQueryProperties })
export type FingerprintsPatch = Static<typeof fingerprintsPatchSchema>
export const fingerprintsPatchValidator = getValidator(fingerprintsPatchSchema, dataValidator)
export const fingerprintsPatchResolver = resolve<FingerprintsPatch, HookContext>({})

// Allow querying on any field from the main schema
export const fingerprintsQuerySchema = queryWrapper(fingerprintsSchema)
export type FingerprintsQuery = Static<typeof fingerprintsQuerySchema>
export const fingerprintsQueryValidator = getValidator(fingerprintsQuerySchema, queryValidator)
export const fingerprintsQueryResolver = resolve<FingerprintsQuery, HookContext>({})
