// TypeBox schema for flow-charts service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, queryWrapper, commonPatch } from '../../utils/common/typebox-schemas.js'

// export const flowChartsSchema = ##ADD TYPEBOX SCHEMA YOU CONVERTED HERE


export type FlowCharts = Static<typeof flowChartsSchema>
export const flowChartsValidator = getValidator(flowChartsSchema, dataValidator)
export const flowChartsResolver = resolve<FlowCharts, HookContext>({})
export const flowChartsExternalResolver = resolve<FlowCharts, HookContext>({})

export const flowChartsDataSchema = Type.Object({
  ...Type.Omit(flowChartsSchema, ['_id']).properties
}, { additionalProperties: false })

export type FlowChartsData = Static<typeof flowChartsDataSchema>
export const flowChartsDataValidator = getValidator(flowChartsDataSchema, dataValidator)
export const flowChartsDataResolver = resolve<FlowChartsData, HookContext>({})

export const flowChartsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(flowChartsSchema, ['_id'])).properties
}, { additionalProperties: false })
export type FlowChartsPatch = Static<typeof flowChartsPatchSchema>
export const flowChartsPatchValidator = getValidator(flowChartsPatchSchema, dataValidator)
export const flowChartsPatchResolver = resolve<FlowChartsPatch, HookContext>({})

// Allow querying on any field from the main schema
const flowChartsQueryProperties = flowChartsSchema
export const flowChartsQuerySchema = queryWrapper(flowChartsQueryProperties)
export type FlowChartsQuery = Static<typeof flowChartsQuerySchema>
export const flowChartsQueryValidator = getValidator(flowChartsQuerySchema, queryValidator)
export const flowChartsQueryResolver = resolve<FlowChartsQuery, HookContext>({})
