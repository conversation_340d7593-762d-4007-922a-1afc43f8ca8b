// TypeBox schema for funds service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, queryWrapper, commonPatch } from '../../utils/common/typebox-schemas.js'

export const fundsSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.String(),
  state: Type.String(),
  board: Type.Optional(Type.Array(ObjectIdSchema())),
  nominees: Type.Optional(Type.Record(Type.String(), Type.Object({
    id: Type.Optional(ObjectIdSchema()),
    status: Type.Optional(Type.Number({ enum: [0, 1, 2, 3, 4, 5] })),
    statusUpdates: Type.Optional(Type.Array(Type.Object({
      from: Type.Optional(Type.Number()),
      to: Type.Optional(Type.Number()),
      at: Type.Optional(Type.Any()),
      by: Type.Optional(ObjectIdSchema()),
    }))),
  }))),
  ...commonFields
}, { $id: 'Funds', additionalProperties: false })

export type Funds = Static<typeof fundsSchema>
export const fundsValidator = getValidator(fundsSchema, dataValidator)
export const fundsResolver = resolve<Funds, HookContext>({})
export const fundsExternalResolver = resolve<Funds, HookContext>({})

export const fundsDataSchema = Type.Object({
  ...Type.Omit(fundsSchema, ['_id']).properties
}, { additionalProperties: false })

export type FundsData = Static<typeof fundsDataSchema>
export const fundsDataValidator = getValidator(fundsDataSchema, dataValidator)
export const fundsDataResolver = resolve<FundsData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const fundsQueryProperties = Type.Pick(fundsSchema, ['_id', 'board', 'nominees'])

export const fundsPatchSchema = commonPatch(fundsSchema, { pushPullOpts: [{ path: 'board', type: ObjectIdSchema() }], pickedForSet: fundsQueryProperties })
export type FundsPatch = Static<typeof fundsPatchSchema>
export const fundsPatchValidator = getValidator(fundsPatchSchema, dataValidator)
export const fundsPatchResolver = resolve<FundsPatch, HookContext>({})

// Allow querying on any field from the main schema
export const fundsQuerySchema = queryWrapper(fundsSchema)
export type FundsQuery = Static<typeof fundsQuerySchema>
export const fundsQueryValidator = getValidator(fundsQuerySchema, queryValidator)
export const fundsQueryResolver = resolve<FundsQuery, HookContext>({})
