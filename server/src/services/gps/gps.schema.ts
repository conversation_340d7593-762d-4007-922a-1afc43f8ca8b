// TypeBox schema for gps service
import {resolve} from '@feathersjs/schema'
import {Type, getValidator, ObjectIdSchema} from '@feathersjs/typebox'
import type {Static} from '@feathersjs/typebox'
import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {commonFields, imageSchema, addToSet, queryWrapper, commonPatch} from '../../utils/common/typebox-schemas.js'
import {employerContribution} from '../plans/schemas/contributions.js';
import {coverageCalcSchema} from '../coverages/schemas/benefits.js';
import {eeSchema} from '../doc-requests/schemas/index.js';

// Premium by key schema
const premiumByKey = Type.Object({
    single: Type.Number(),
    plus_spouse: Type.Number(),
    plus_child: Type.Number(),
    plus_child__2: Type.Number(),
    plus_child__3: Type.Number(),
    family: Type.Number()
} as const);

// Simulation base schema
const simBase = Type.Object({
    count: Type.Number({description: 'Total number of auto-selected plans'}),
    spendPtc: Type.Number({description: 'premium + oop for auto-selected plan factoring in PTC'}),
    premiumPtc: Type.Number({description: 'premium for auto-selected plan factoring in PTC'}),
    spend: Type.Number({description: 'premium + oop for auto-selected plan'}),
    premium: Type.Number({description: 'premium for auto-selected plan'}),
    tax_savings: Type.Number({description: 'Total tax savings for auto-selected plan if ICHRA'}),
    tax_rate: Type.Number({description: 'Average tax rate for auto-selected plan employees - useful for group calculations'}),
    ptc: Type.Number({description: 'Total PTC for auto-selected plan'}),
    ptc_likely: Type.Number({description: 'Likely actual PTC based on whether the best option was PTC eligible'}),
    altSpend: Type.Number({description: 'Best non insurance option'}),
    altPremium: Type.Number(),
    selected_spend: Type.Number(),
    selected_spend_delta: Type.Number(),
    selected_spendPtc_delta: Type.Number(),
    selected_premium: Type.Number(),
    selected_premium_delta: Type.Number({description: 'The difference in premium between the auto-selected plan and the employee selected plan'}),
    selected_premiumPtc_delta: Type.Number({description: 'The difference in premium between the auto-selected plan and the employee selected plan'}),
    selected_ptc: Type.Number({description: 'PTC applying to selected plans'}),
    selected_ptc_delta: Type.Number({description: 'The difference in PTC between the auto-selected plan and the employee selected plan'}),
    selected_count: Type.Number(),
    selected_tax_savings: Type.Number(),
    selected_tax_savings_delta: Type.Number(),
    selected_tax_rate: Type.Number()
} as const);

const inactiveStats = () => {
    const obj = {};
    for (const k in simBase.properties) {
        obj[`inactive_${k}`] = simBase.properties[k];
    }
    return Type.Object(obj);
}

/** simStats (active + inactive_*) */
const simStats = Type.Object(
    {
        ...(simBase as any).properties,
        ...inactiveStats().properties
    },
    {description: 'Split the stats into enrolled employees (no prefix) and not enrolled "inactive"'}
);


// Employee schema (simplified version of eeSchema)
const ees = Type.Object({
    shop: Type.Optional(ObjectIdSchema()),
    coverage: Type.Optional(Type.String()),
    sim: Type.Optional(ObjectIdSchema()),
    simError: Type.Optional(Type.String()),
    bestPlan: Type.Optional(Type.String()),
    bestPlanPtc: Type.Optional(Type.String()),
    bestAlt: Type.Optional(Type.String()),
    // Add other common employee fields as needed
    household: Type.Optional(Type.String()),
    ...eeSchema.properties
}, {additionalProperties: true})

const currentStats = Type.Object({
    spend: Type.Number(),
    spendCount: Type.Number(),
    spendPremium: Type.Number(),
    count: Type.Number(),
    premium: Type.Number(),
    premiumByKey,
    countByKey: premiumByKey
});

export const gpsSchema = Type.Object({
    _id: ObjectIdSchema(),
    org: Type.Optional(ObjectIdSchema()),
    plan: Type.Optional(ObjectIdSchema()),
    runRequest: Type.Optional(Type.Any()),
    companyName: Type.Optional(Type.String()),
    companyAvatar: Type.Optional(Type.String()),
    email: Type.Optional(Type.String()),
    name: Type.Optional(Type.String()),
    eeCount: Type.Optional(Type.Number()),
    planName: Type.Optional(Type.String()),
    employerContribution, // This would be the employerContribution schema from plans
    ale: Type.Optional(Type.Boolean()),
    owner: Type.Optional(ObjectIdSchema()), // person
    editors: Type.Optional(Type.Array(ObjectIdSchema())),
    vectorId: Type.Optional(Type.String()),
    lastSim: Type.Optional(Type.Any()),
    groupCompare: Type.Optional(Type.Boolean()),
    simProgress: Type.Optional(Type.Number()),
    simStats,
    currentStats: Type.Optional(Type.Object({
        spend: Type.Optional(Type.Number()),
        spendCount: Type.Optional(Type.Number()),
        spendPremium: Type.Optional(Type.Number()),
        count: Type.Optional(Type.Number()),
        premium: Type.Optional(Type.Number()),
        premiumByKey: Type.Optional(premiumByKey),
        countByKey: Type.Optional(premiumByKey)
    }, {additionalProperties: false})),
    employees: Type.Optional(Type.Array(ees)),
    coverages: Type.Optional(Type.Record(Type.String(), Type.Object({
        id: Type.Optional(Type.String()),
        compare_id: Type.Optional(Type.String()),
        similar: Type.Optional(Type.Record(Type.String(), Type.String())),
        mostSimilar: Type.Optional(Type.String()),
        ...coverageCalcSchema.properties,
        knownKeys: Type.Optional(Type.Array(Type.String())),
        files: Type.Optional(Type.Array(imageSchema)),
        fromFile: Type.Optional(Type.Boolean())
    }, {additionalProperties: false}))),
    employerContributionReports: Type.Optional(Type.Record(Type.String(), Type.Object({
        person: Type.Optional(ObjectIdSchema()),
        gps: Type.Optional(ObjectIdSchema()),
        updatedAt: Type.Optional(Type.Any()),
        data: employerContribution // This would be the employerContribution schema
    }, {additionalProperties: false}))),
    ...commonFields
}, {additionalProperties: false})

export type Gps = Static<typeof gpsSchema>
export const gpsValidator = getValidator(gpsSchema, dataValidator)
export const gpsResolver = resolve<Gps, HookContext>({})
export const gpsExternalResolver = resolve<Gps, HookContext>({})

export const gpsDataSchema = Type.Object({
    ...Type.Omit(gpsSchema, ['_id']).properties
}, {additionalProperties: false})

export type GpsData = Static<typeof gpsDataSchema>
export const gpsDataValidator = getValidator(gpsDataSchema, dataValidator)
export const gpsDataResolver = resolve<GpsData, HookContext>({})
const gpsQueryProperties = Type.Pick(gpsSchema, ['_id', 'org', 'plan', 'owner', 'editors', 'createdBy', 'updatedBy'])
export const gpsPatchSchema = commonPatch(gpsSchema, {
    pushPullOpts: [{path: 'rates', type: ObjectIdSchema()}],
    pickedForSet: gpsQueryProperties
})
export type GpsPatch = Static<typeof gpsPatchSchema>
export const gpsPatchValidator = getValidator(gpsPatchSchema, dataValidator)
export const gpsPatchResolver = resolve<GpsPatch, HookContext>({})

export const gpsQuerySchema = queryWrapper(gpsQueryProperties)
export type GpsQuery = Static<typeof gpsQuerySchema>
export const gpsQueryValidator = getValidator(gpsQuerySchema, queryValidator)
export const gpsQueryResolver = resolve<GpsQuery, HookContext>({})
