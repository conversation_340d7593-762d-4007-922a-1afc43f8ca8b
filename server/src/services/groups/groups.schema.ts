// TypeBox schema for groups service
import {resolve} from '@feathersjs/schema'
import {Type, getValidator, ObjectIdSchema} from '@feathersjs/typebox'
import type {Static} from '@feathersjs/typebox'
import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {commonFields, queryWrapper, commonPatch} from '../../utils/common/typebox-schemas.js'

export const groupsSchema = Type.Object({
    _id: ObjectIdSchema(),
    org: ObjectIdSchema(),
    key: Type.String(),
    name: Type.String(),
    description: Type.Optional(Type.String()),
    applyUcan: Type.Optional(Type.String()),
    healthPlans: Type.Optional(Type.Array(ObjectIdSchema())),
    planClass: Type.Optional(Type.Boolean()),
    memberCount: Type.Optional(Type.Number()),
    memberCountAt: Type.Optional(Type.Any()),
    ...commonFields
}, {$id: 'Groups', additionalProperties: false})

export type Groups = Static<typeof groupsSchema>
export const groupsValidator = getValidator(groupsSchema, dataValidator)
export const groupsResolver = resolve<Groups, HookContext>({})
export const groupsExternalResolver = resolve<Groups, HookContext>({})

// Schema for creating new data
export const groupsDataSchema = Type.Object({
    ...Type.Omit(groupsSchema, ['_id']).properties
}, {additionalProperties: false})

export type GroupsData = Static<typeof groupsDataSchema>
export const groupsDataValidator = getValidator(groupsDataSchema, dataValidator)
export const groupsDataResolver = resolve<GroupsData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const groupsQueryProperties = Type.Pick(groupsSchema, ['_id', 'org', 'healthPlans'])

export const groupsPatchSchema = commonPatch(groupsSchema, {
    pushPullOpts: [
        {path: 'healthPlans', type: ObjectIdSchema()},
        {path: 'members', type: ObjectIdSchema()},
    ],
    pickedForSet: groupsQueryProperties
})

export type GroupsPatch = Static<typeof groupsPatchSchema>
export const groupsPatchValidator = getValidator(groupsPatchSchema, dataValidator)
export const groupsPatchResolver = resolve<GroupsPatch, HookContext>({})

// Allow querying on any field from the main schema
export const groupsQuerySchema = queryWrapper(groupsSchema)

export type GroupsQuery = Static<typeof groupsQuerySchema>
export const groupsQueryValidator = getValidator(groupsQuerySchema, queryValidator)
export const groupsQueryResolver = resolve<GroupsQuery, HookContext>({})
