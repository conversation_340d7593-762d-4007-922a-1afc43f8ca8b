// TypeBox schema for grp-mbrs service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, queryWrapper, commonPatch } from '../../utils/common/typebox-schemas.js'

export const grpMbrsSchema = Type.Object({
  _id: ObjectIdSchema(),
  group: ObjectIdSchema(),
  person: ObjectIdSchema(),
  org: ObjectIdSchema(),
  mbrId: Type.String(),
  name: Type.Optional(Type.String()),
  email: Type.Optional(Type.String()),
  ...commonFields
}, { $id: 'GrpMbrs', additionalProperties: false })

export type GrpMbrs = Static<typeof grpMbrsSchema>
export const grpMbrsValidator = getValidator(grpMbrsSchema, dataValidator)
export const grpMbrsResolver = resolve<GrpMbrs, HookContext>({})
export const grpMbrsExternalResolver = resolve<GrpMbrs, HookContext>({})

export const grpMbrsDataSchema = Type.Object({
  ...Type.Omit(grpMbrsSchema, ['_id']).properties
}, { additionalProperties: false })

export type GrpMbrsData = Static<typeof grpMbrsDataSchema>
export const grpMbrsDataValidator = getValidator(grpMbrsDataSchema, dataValidator)
export const grpMbrsDataResolver = resolve<GrpMbrsData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const grpMbrsQueryProperties = Type.Pick(grpMbrsSchema, ['_id', 'group', 'person', 'org'])

export const grpMbrsPatchSchema = commonPatch(grpMbrsSchema, { pushPullOpts: [], pickedForSet: grpMbrsQueryProperties })
export type GrpMbrsPatch = Static<typeof grpMbrsPatchSchema>
export const grpMbrsPatchValidator = getValidator(grpMbrsPatchSchema, dataValidator)
export const grpMbrsPatchResolver = resolve<GrpMbrsPatch, HookContext>({})

// Allow querying on any field from the main schema
export const grpMbrsQuerySchema = queryWrapper(grpMbrsSchema)
export type GrpMbrsQuery = Static<typeof grpMbrsQuerySchema>
export const grpMbrsQueryValidator = getValidator(grpMbrsQuerySchema, queryValidator)
export const grpMbrsQueryResolver = resolve<GrpMbrsQuery, HookContext>({})
