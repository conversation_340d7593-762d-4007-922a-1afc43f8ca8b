// TypeBox schema for health-shares service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, queryWrapper, commonPatch, imageSchema, videoSchema } from '../../utils/common/typebox-schemas.js'
import {vectorStore} from '../shops/schemas/index.js';



export const healthSharesSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.String(),
  description: Type.Optional(Type.String()),
  aka: Type.Optional(Type.Array(Type.String())),
  logo: Type.Optional(imageSchema),
  cc_video: Type.Optional(videoSchema),
  video: Type.Optional(videoSchema),
  files: Type.Optional(Type.Record(Type.String(), imageSchema)),
  products: Type.Optional(Type.Record(Type.String(), Type.Object({
    id: Type.Optional(Type.String()),
    name: Type.Optional(Type.String()),
    guidelines: Type.Optional(imageSchema),
    vectorStore,
  }))),
  financials: Type.Optional(Type.Record(Type.String(), Type.Object({
    total_revenue: Type.Optional(Type.Number()),
    sharing_expense: Type.Optional(Type.Number()),
    admin_expense: Type.Optional(Type.Number()),
    net_assets_start: Type.Optional(Type.Number()),
    net_assets: Type.Optional(Type.Number()),
    cash_on_hand: Type.Optional(Type.Number()),
    highest_paid_executive: Type.Optional(Type.Number()),
  }))),
  ...commonFields
}, { $id: 'HealthShares', additionalProperties: false })

export type HealthShares = Static<typeof healthSharesSchema>
export const healthSharesValidator = getValidator(healthSharesSchema, dataValidator)
export const healthSharesResolver = resolve<HealthShares, HookContext>({})
export const healthSharesExternalResolver = resolve<HealthShares, HookContext>({})

export const healthSharesDataSchema = Type.Object({
  ...Type.Omit(healthSharesSchema, ['_id']).properties
}, { additionalProperties: false })

export type HealthSharesData = Static<typeof healthSharesDataSchema>
export const healthSharesDataValidator = getValidator(healthSharesDataSchema, dataValidator)
export const healthSharesDataResolver = resolve<HealthSharesData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const healthSharesQueryProperties = Type.Pick(healthSharesSchema, ['_id'])

export const healthSharesPatchSchema = commonPatch(healthSharesSchema, { pushPullOpts: [], pickedForSet: healthSharesQueryProperties })
export type HealthSharesPatch = Static<typeof healthSharesPatchSchema>
export const healthSharesPatchValidator = getValidator(healthSharesPatchSchema, dataValidator)
export const healthSharesPatchResolver = resolve<HealthSharesPatch, HookContext>({})

// Allow querying on any field from the main schema
export const healthSharesQuerySchema = queryWrapper(healthSharesSchema)
export type HealthSharesQuery = Static<typeof healthSharesQuerySchema>
export const healthSharesQueryValidator = getValidator(healthSharesQuerySchema, queryValidator)
export const healthSharesQueryResolver = resolve<HealthSharesQuery, HookContext>({})
