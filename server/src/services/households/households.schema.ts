// TypeBox schema for households service
import {resolve} from '@feathersjs/schema'
import {Type, getValidator, ObjectIdSchema} from '@feathersjs/typebox'
import type {Static} from '@feathersjs/typebox'
import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {commonFields, queryWrapper, commonPatch, addressSchema} from '../../utils/common/typebox-schemas.js'
import {relationships} from './schemas/enums.js'

const enrollExtras = Type.Object({
    monthsSinceSmoked: Type.Optional(Type.Number()),
    disabled: Type.Optional(Type.Boolean()),
    incarcerated: Type.Optional(Type.Boolean()),
    latino: Type.Optional(Type.Boolean()),
    native: Type.Optional(Type.Boolean()),
    pregnant: Type.Optional(Type.Boolean()),
    us_citizen: Type.Optional(Type.Boolean()),
    adl_assist: Type.Optional(Type.Boolean()),
    medicaid: Type.Optional(Type.Boolean()),
    medicaid_ineligible: Type.Optional(Type.Boolean()),
    outside_coverage: Type.Optional(Type.Boolean()),
    outside_coverage_end: Type.Optional(Type.String()),
    job_coverage: Type.Optional(Type.Boolean())
})
export const householdsSchema = Type.Object({
    _id: ObjectIdSchema(),
    person: ObjectIdSchema(),
    filingAs: Type.Optional(Type.Union([
        Type.Literal("s"),
        Type.Literal("ms"),
        Type.Literal("hh"),
        Type.Literal("mj"),
    ])),
    providers: Type.Optional(Type.Array(ObjectIdSchema())),
    practitioners: Type.Optional(Type.Array(ObjectIdSchema())),
    magi: Type.Optional(Type.Number()),
    qual_events: Type.Optional(Type.Record(Type.String(), Type.String())),
    incomes: Type.Optional(Type.Record(Type.String(), Type.Object({
        name: Type.Optional(Type.String()),
        amount: Type.Optional(Type.Number()),
        off: Type.Optional(Type.Boolean()),
        interval: Type.Optional(Type.Union([
            Type.Literal("hour"),
            Type.Literal("day"),
            Type.Literal("week"),
            Type.Literal("month"),
            Type.Literal("quarter"),
            Type.Literal("year"),
            Type.Literal("once"),
        ])),
        class: Type.Optional(Type.Union([
            Type.Literal("ee"),
            Type.Literal("ic"),
        ])),
        estHours: Type.Optional(Type.Number()),
    }))),
    deductions: Type.Optional(Type.Record(Type.String(), Type.Object({
        off: Type.Optional(Type.Boolean()),
        amount: Type.Optional(Type.Number()),
        atl: Type.Optional(Type.Boolean()),
    }))),
    ...enrollExtras.properties,
    members: Type.Optional(Type.Record(Type.String(), Type.Object({
        relation: Type.Optional(Type.String({enum: relationships})),
        dependent: Type.Optional(Type.Boolean()),
        annualIncome: Type.Optional(Type.Number()),
        address: Type.Optional(addressSchema),
        ...enrollExtras.properties
    }))),
    ...commonFields
}, {$id: 'Households', additionalProperties: false})

export type Households = Static<typeof householdsSchema>
export const householdsValidator = getValidator(householdsSchema, dataValidator)
export const householdsResolver = resolve<Households, HookContext>({})
export const householdsExternalResolver = resolve<Households, HookContext>({})

// Schema for creating new data
export const householdsDataSchema = Type.Object({
    ...Type.Omit(householdsSchema, ['_id']).properties
}, {additionalProperties: false})

export type HouseholdsData = Static<typeof householdsDataSchema>
export const householdsDataValidator = getValidator(householdsDataSchema, dataValidator)
export const householdsDataResolver = resolve<HouseholdsData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const householdsQueryProperties = Type.Pick(householdsSchema, ['_id', 'person', 'providers', 'practitioners'])

export const householdsPatchSchema = commonPatch(householdsSchema, {
    pushPullOpts: [{
        path: 'providers',
        type: ObjectIdSchema()
    }, {path: 'practitioners', type: ObjectIdSchema()}], pickedForSet: householdsQueryProperties
})

export type HouseholdsPatch = Static<typeof householdsPatchSchema>
export const householdsPatchValidator = getValidator(householdsPatchSchema, dataValidator)
export const householdsPatchResolver = resolve<HouseholdsPatch, HookContext>({})

// Allow querying on any field from the main schema
export const householdsQuerySchema = queryWrapper(householdsSchema)

export type HouseholdsQuery = Static<typeof householdsQuerySchema>
export const householdsQueryValidator = getValidator(householdsQuerySchema, queryValidator)
export const householdsQueryResolver = resolve<HouseholdsQuery, HookContext>({})
