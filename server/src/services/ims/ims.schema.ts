// TypeBox schema for ims service
import {resolve} from '@feathersjs/schema'
import {Type, getValidator, ObjectIdSchema} from '@feathersjs/typebox'
import type {Static} from '@feathersjs/typebox'
import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {commonFields, queryWrapper, commonPatch} from '../../utils/common/typebox-schemas.js'

const sendTo = Type.Union([
    Type.Literal("in-app"),
    Type.Literal("phone"),
    Type.Literal("email"),
])

const pSchema = Type.Object({
    login: Type.Optional(ObjectIdSchema()),
    fp: Type.Optional(Type.String()),
    name: Type.Optional(Type.String()),
    email: Type.Optional(Type.String()),
    phone: Type.Optional(Type.String()),
    sendTo: Type.Optional(sendTo),
    lastAt: Type.Optional(Type.Any()),
    status: Type.Optional(Type.Union([Type.Literal(0), Type.Literal(1), Type.Literal(2)])),
    offline: Type.Optional(Type.Boolean()),
}, {additionalProperties: false})

const msgSchema = Type.Object({
    id: Type.Optional(Type.String()),
    sentAt: Type.Optional(Type.Any()),
    body: Type.Optional(Type.String()),
    pid: Type.Optional(Type.String()),
    source: Type.Optional(Type.Union([
        Type.Literal("in-app"),
        Type.Literal("phone"),
        Type.Literal("email"),
    ])),
    openedBy: Type.Optional(Type.Array(Type.String())),
    errs: Type.Optional(Type.Record(Type.String(), Type.Object({
        code: Type.Optional(Type.Number()),
        message: Type.Optional(Type.String()),
        pid: Type.Optional(Type.String()),
    }))),
})

export const imsSchema = Type.Object({
    _id: ObjectIdSchema(),
    subject: Type.Optional(ObjectIdSchema()),
    subjectService: Type.Optional(Type.String()),
    plan: Type.Optional(ObjectIdSchema()),
    person: Type.Optional(ObjectIdSchema()),
    team: Type.Optional(ObjectIdSchema()),
    participant: Type.Optional(pSchema),
    views: Type.Optional(Type.Array(Type.Object({
        id: Type.Optional(Type.String()),
        at: Type.Optional(Type.Any()),
    }))),
    orphan: Type.Optional(Type.Boolean()),
    texting: Type.Optional(Type.Boolean()),
    calling: Type.Optional(Type.Boolean()),
    typing: Type.Optional(Type.Array(Type.String())),
    support: Type.Optional(Type.Record(Type.String(), pSchema)),
    messages: Type.Optional(Type.Array(msgSchema)),
    ...commonFields
}, {$id: 'Ims', additionalProperties: false})


export type Ims = Static<typeof imsSchema>
export const imsValidator = getValidator(imsSchema, dataValidator)
export const imsResolver = resolve<Ims, HookContext>({})
export const imsExternalResolver = resolve<Ims, HookContext>({})

export const imsDataSchema = Type.Object({
    ...Type.Omit(imsSchema, ['_id']).properties
}, {additionalProperties: false})

export type ImsData = Static<typeof imsDataSchema>
export const imsDataValidator = getValidator(imsDataSchema, dataValidator)
export const imsDataResolver = resolve<ImsData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const imsQueryProperties = Type.Pick(imsSchema, ['_id', 'subject', 'plan', 'person', 'team', 'participant', 'support'])

export const imsPatchSchema = commonPatch(imsSchema, {
    pushPullOpts: [
        {path: 'messages', type: msgSchema},
        {path: 'typing', type: {type: 'string'}}
    ]
    , pickedForSet: imsQueryProperties
})
export type ImsPatch = Static<typeof imsPatchSchema>
export const imsPatchValidator = getValidator(imsPatchSchema, dataValidator)
export const imsPatchResolver = resolve<ImsPatch, HookContext>({})

// Allow querying on any field from the main schema
export const imsQuerySchema = queryWrapper(imsSchema)
export type ImsQuery = Static<typeof imsQuerySchema>
export const imsQueryValidator = getValidator(imsQuerySchema, queryValidator)
export const imsQueryResolver = resolve<ImsQuery, HookContext>({})
