// TypeBox schema for issues service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet, queryWrapper, commonPatch } from '../../utils/common/typebox-schemas.js'

export const issuesSchema = Type.Object({
  _id: ObjectIdSchema(),
  service: Type.Optional(Type.String()),
  record: Type.Optional(ObjectIdSchema()),
  type: Type.Optional(Type.Union([
    Type.Literal("content"),
    Type.Literal("complaint"),
    Type.Literal("dispute"),
  ])),
  category: Type.Optional(Type.String()),
  message: Type.Optional(Type.String()),
  by: ObjectIdSchema(),
  org: Type.Optional(ObjectIdSchema()),
  threads: Type.Optional(Type.Array(ObjectIdSchema())),
  status: Type.Optional(Type.Union([
    Type.Literal("unopened"),
    Type.Literal("assigned"),
    Type.Literal("resolved"),
    Type.Literal("escalated"),
    Type.Literal("dropped"),
  ])),
  transaction: Type.Optional(Type.String()),
  resolvedAt: Type.Optional(Type.Any()),
  channel: Type.Optional(Type.Union([
    Type.Literal("app"),
    Type.Literal("email"),
    Type.Literal("phone"),
    Type.Literal("mail"),
  ])),
  assigned: Type.Optional(ObjectIdSchema()),
  assignedAt: Type.Optional(Type.Any()),
  assignedHistory: Type.Optional(Type.Array(Type.Object({
    id: Type.Optional(ObjectIdSchema()),
    at: Type.Optional(Type.Any()),
    notes: Type.Optional(Type.String()),
  }))),
  treasuryComplaint: Type.Optional(Type.Boolean()),
  treasuryData: Type.Optional(Type.Object({
    date_input: Type.Optional(Type.String()),
    date_received: Type.Optional(Type.String()),
    user_name: Type.Optional(Type.String()),
    received_by: Type.Optional(Type.String()),
    account_ID: Type.Optional(Type.String()),
    complaint_classification: Type.Optional(Type.Union([Type.Literal("operational"), Type.Literal("executive")])),
    complaint_category: Type.Optional(Type.Union([
      Type.Literal("Privacy or Security"),
      Type.Literal("Legal or Regulatory"),
      Type.Literal("Product or Service"),
    ])),
    complaint_sub_category: Type.Optional(Type.String()),
    complaint_description: Type.Optional(Type.String()),
    alleges_UDAP_or_discrimination: Type.Optional(Type.Boolean()),
    user_stage: Type.Optional(Type.Union([
      Type.Literal("prospect"),
      Type.Literal("onboarding"),
      Type.Literal("active"),
    ])),
    systemic_issue_identified: Type.Optional(Type.Boolean()),
    date_resolved: Type.Optional(Type.String()),
    redress_reqd: Type.Optional(Type.Boolean()),
    description_of_corrective_action: Type.Optional(Type.String()),
    internal_links: Type.Optional(Type.Array(Type.String())),
    user_correspondence_links: Type.Optional(Type.Array(Type.String())),
    reason_exceeded_15_days: Type.Optional(Type.String()),
  })),
  ...commonFields
}, { $id: 'Issues', additionalProperties: false })

export type Issues = Static<typeof issuesSchema>
export const issuesValidator = getValidator(issuesSchema, dataValidator)
export const issuesResolver = resolve<Issues, HookContext>({})
export const issuesExternalResolver = resolve<Issues, HookContext>({})

export const issuesDataSchema = Type.Object({
  ...Type.Omit(issuesSchema, ['_id']).properties
}, { additionalProperties: false })

export type IssuesData = Static<typeof issuesDataSchema>
export const issuesDataValidator = getValidator(issuesDataSchema, dataValidator)
export const issuesDataResolver = resolve<IssuesData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const issuesQueryProperties = Type.Pick(issuesSchema, ['_id', 'record', 'by', 'org', 'threads', 'assigned', 'assignedHistory'])

export const issuesPatchSchema = commonPatch(issuesSchema, { pushPullOpts: [{ path: 'threads', type: ObjectIdSchema() }], pickedForSet: issuesQueryProperties })
export type IssuesPatch = Static<typeof issuesPatchSchema>
export const issuesPatchValidator = getValidator(issuesPatchSchema, dataValidator)
export const issuesPatchResolver = resolve<IssuesPatch, HookContext>({})

// Allow querying on any field from the main schema
export const issuesQuerySchema = queryWrapper(issuesSchema)
export type IssuesQuery = Static<typeof issuesQuerySchema>
export const issuesQueryValidator = getValidator(issuesQuerySchema, queryValidator)
export const issuesQueryResolver = resolve<IssuesQuery, HookContext>({})
