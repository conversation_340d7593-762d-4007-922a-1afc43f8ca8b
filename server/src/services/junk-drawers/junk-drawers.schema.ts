// TypeBox schema for junk-drawers service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, queryWrapper, commonPatch } from '../../utils/common/typebox-schemas.js'

export const junkDrawersSchema = Type.Object({
  _id: ObjectIdSchema(),
  drawer: Type.String(),
  itemName: Type.String(),
  itemId: Type.String(),
  data: Type.Optional(Type.Any()),
  ...commonFields
}, { $id: 'JunkDrawers', additionalProperties: false })


export type JunkDrawers = Static<typeof junkDrawersSchema>
export const junkDrawersValidator = getValidator(junkDrawersSchema, dataValidator)
export const junkDrawersResolver = resolve<JunkDrawers, HookContext>({})
export const junkDrawersExternalResolver = resolve<JunkDrawers, HookContext>({})

export const junkDrawersDataSchema = Type.Object({
  ...Type.Omit(junkDrawersSchema, ['_id']).properties
}, { additionalProperties: false })

export type JunkDrawersData = Static<typeof junkDrawersDataSchema>
export const junkDrawersDataValidator = getValidator(junkDrawersDataSchema, dataValidator)
export const junkDrawersDataResolver = resolve<JunkDrawersData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const junkDrawersQueryProperties = Type.Pick(junkDrawersSchema, ['_id'])

export const junkDrawersPatchSchema = commonPatch(junkDrawersSchema, { pushPullOpts: [], pickedForSet: junkDrawersQueryProperties })
export type JunkDrawersPatch = Static<typeof junkDrawersPatchSchema>
export const junkDrawersPatchValidator = getValidator(junkDrawersPatchSchema, dataValidator)
export const junkDrawersPatchResolver = resolve<JunkDrawersPatch, HookContext>({})

// Allow querying on any field from the main schema
export const junkDrawersQuerySchema = queryWrapper(junkDrawersSchema)
export type JunkDrawersQuery = Static<typeof junkDrawersQuerySchema>
export const junkDrawersQueryValidator = getValidator(junkDrawersQuerySchema, queryValidator)
export const junkDrawersQueryResolver = resolve<JunkDrawersQuery, HookContext>({})
