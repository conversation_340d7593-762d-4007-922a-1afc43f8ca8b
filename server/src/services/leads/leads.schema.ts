// TypeBox schema for leads service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, queryWrapper, commonPatch } from '../../utils/common/typebox-schemas.js'

export const leadsSchema = Type.Object({
  _id: ObjectIdSchema(),
  ...commonFields
}, { $id: 'Leads', additionalProperties: true })


export type Leads = Static<typeof leadsSchema>
export const leadsValidator = getValidator(leadsSchema, dataValidator)
export const leadsResolver = resolve<Leads, HookContext>({})
export const leadsExternalResolver = resolve<Leads, HookContext>({})

export const leadsDataSchema = Type.Object({
  ...Type.Omit(leadsSchema, ['_id']).properties
}, { additionalProperties: false })

export type LeadsData = Static<typeof leadsDataSchema>
export const leadsDataValidator = getValidator(leadsDataSchema, dataValidator)
export const leadsDataResolver = resolve<LeadsData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const leadsQueryProperties = Type.Pick(leadsSchema, ['_id'])

export const leadsPatchSchema = commonPatch(leadsSchema, { pushPullOpts: [], pickedForSet: leadsQueryProperties })
export type LeadsPatch = Static<typeof leadsPatchSchema>
export const leadsPatchValidator = getValidator(leadsPatchSchema, dataValidator)
export const leadsPatchResolver = resolve<LeadsPatch, HookContext>({})

// Allow querying on any field from the main schema
export const leadsQuerySchema = queryWrapper(leadsSchema)
export type LeadsQuery = Static<typeof leadsQuerySchema>
export const leadsQueryValidator = getValidator(leadsQuerySchema, queryValidator)
export const leadsQueryResolver = resolve<LeadsQuery, HookContext>({})
