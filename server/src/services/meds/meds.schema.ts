// TypeBox schema for meds service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet, pull, queryWrapper } from '../../utils/common/typebox-schemas.js'

const medInfoPatternSchema = Type.Object({
  rxcui: Type.Optional(Type.String()),
  name: Type.Optional(Type.String()),
  synonym: Type.Optional(Type.String()),
  language: Type.Optional(Type.String()),
  suppress: Type.Optional(Type.String()),
  umlscui: Type.Optional(Type.String())
}, { additionalProperties: true })

export const medsSchema = Type.Object({
  _id: ObjectIdSchema,
  rxcui: Type.String(),
  standard: Type.Optional(Type.String()),
  rxcuis: Type.Optional(Type.Array(Type.String())),
  s_f: Type.Optional(Type.Array(Type.String())),
  variants: Type.Optional(Type.Array(Type.String())),
  name: Type.Optional(Type.String()),
  medical_name: Type.Optional(Type.String()),
  consumer_name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  activeIngredient: Type.Optional(Type.String()),
  synonyms: Type.Optional(Type.String()),
  sbdOf: Type.Optional(ObjectIdSchema),
  ndcs: Type.Optional(Type.Array(Type.String())),
  info: Type.Optional(Type.Object({
    IN: Type.Optional(Type.Object({}, { patternProperties: { "^.*$": medInfoPatternSchema } })),
    PIN: Type.Optional(Type.Object({}, { patternProperties: { "^.*$": medInfoPatternSchema } })),
    MIN: Type.Optional(Type.Object({}, { patternProperties: { "^.*$": medInfoPatternSchema } })),
    SCD: Type.Optional(Type.Object({}, { patternProperties: { "^.*$": medInfoPatternSchema } })),
    SCDF: Type.Optional(Type.Object({}, { patternProperties: { "^.*$": medInfoPatternSchema } })),
    SCDG: Type.Optional(Type.Object({}, { patternProperties: { "^.*$": medInfoPatternSchema } })),
    SBD: Type.Optional(Type.Object({}, { patternProperties: { "^.*$": medInfoPatternSchema } })),
    SBDF: Type.Optional(Type.Object({}, { patternProperties: { "^.*$": medInfoPatternSchema } })),
    SBDG: Type.Optional(Type.Object({}, { patternProperties: { "^.*$": medInfoPatternSchema } })),
    GPCK: Type.Optional(Type.Object({}, { patternProperties: { "^.*$": medInfoPatternSchema } })),
    BN: Type.Optional(Type.Object({}, { patternProperties: { "^.*$": medInfoPatternSchema } })),
    BPCK: Type.Optional(Type.Object({}, { patternProperties: { "^.*$": medInfoPatternSchema } }))
  }))
}, { additionalProperties: false })

export type Meds = Static<typeof medsSchema>
export const medsValidator = getValidator(medsSchema, dataValidator)
export const medsResolver = resolve<Meds, HookContext>({})
export const medsExternalResolver = resolve<Meds, HookContext>({})

export const medsDataSchema = Type.Object({
  ...Type.Omit(medsSchema, ['_id']).properties
}, { additionalProperties: false })

export type MedsData = Static<typeof medsDataSchema>
export const medsDataValidator = getValidator(medsDataSchema, dataValidator)
export const medsDataResolver = resolve<MedsData, HookContext>({})

export const medsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(medsSchema, ['_id'])).properties
,
  // Missing MongoDB operators
  $addToSet: Type.Optional(addToSet([])),
  $pull: Type.Optional(pull([])),
}, { additionalProperties: false })
export type MedsPatch = Static<typeof medsPatchSchema>
export const medsPatchValidator = getValidator(medsPatchSchema, dataValidator)
export const medsPatchResolver = resolve<MedsPatch, HookContext>({})

// Allow querying on any field from the main schema
// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility
const medsQueryProperties = Type.Object({
  ...Type.Pick(medsSchema, ['_id', 'interactions', 'createdBy', 'updatedBy']).properties
}, { additionalProperties: true })
export const medsQuerySchema = queryWrapper(medsQueryProperties)
export type MedsQuery = Static<typeof medsQuerySchema>
export const medsQueryValidator = getValidator(medsQuerySchema, queryValidator)
export const medsQueryResolver = resolve<MedsQuery, HookContext>({})
