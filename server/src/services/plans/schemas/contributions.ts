import {Type} from '@feathersjs/typebox';

export const employerContribution = Type.Object({
    amount: Type.Number(),
    family: Type.Number(),
    match: Type.Boolean(),
    type: Type.String({enum: ['percent', 'flat']}),
    percentType: Type.String({enum: ['cost', 'income']}),
    postTax: Type.Number(), //percent of normal contribution to give for post tax elections
    includeExtras: Type.Boolean(),
})

