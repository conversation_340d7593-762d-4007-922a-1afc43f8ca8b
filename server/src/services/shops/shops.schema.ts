// TypeBox schema for shops service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, pull, queryWrapper } from '../../utils/common/typebox-schemas.js'
const peopleSchema = Type.Object({
  _id: ObjectIdSchema(),
  inactive: Type.Optional(Type.Boolean()),
  age: Type.Optional(Type.Number()),
  gender: Type.Optional(Type.Union([Type.Literal("male"), Type.Literal("female")])),
  child: Type.Optional(Type.Boolean()),
  smoker: Type.Optional(Type.Boolean()),
  relation: Type.Optional(Type.String())
}, { additionalProperties: false })

const placeSchema = Type.Object({
  countyfips: Type.String(),
  zipcode: Type.String(),
  state: Type.String()
}, { additionalProperties: false })


export const shopsSchema = Type.Object({
  _id: ObjectIdSchema(),
  org: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  plans: Type.Optional(Type.Array(ObjectIdSchema())),
  coverages: Type.Optional(Type.Array(ObjectIdSchema())),
  market: Type.Optional(ObjectIdSchema()),
  region: Type.Optional(Type.String()),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
,
  // Missing fields from old schema
  person: Type.Optional(ObjectIdSchema()),
  plan: Type.Optional(ObjectIdSchema()),
  enrollment: Type.Optional(ObjectIdSchema()),
  plan_coverage: Type.Optional(Type.String()),
  aiChatCount: Type.Optional(Type.Number()),
}, { additionalProperties: false })

export type Shops = Static<typeof shopsSchema>
export const shopsValidator = getValidator(shopsSchema, dataValidator)
export const shopsResolver = resolve<Shops, HookContext>({})
export const shopsExternalResolver = resolve<Shops, HookContext>({})

export const shopsDataSchema = Type.Object({
  ...Type.Omit(shopsSchema, ['_id']).properties
}, { additionalProperties: false })

export type ShopsData = Static<typeof shopsDataSchema>
export const shopsDataValidator = getValidator(shopsDataSchema, dataValidator)
export const shopsDataResolver = resolve<ShopsData, HookContext>({})

export const shopsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(shopsSchema, ['_id'])).properties
,
  // Missing MongoDB operators
  $pull: Type.Optional(pull([])),
}, { additionalProperties: false })
export type ShopsPatch = Static<typeof shopsPatchSchema>
export const shopsPatchValidator = getValidator(shopsPatchSchema, dataValidator)
export const shopsPatchResolver = resolve<ShopsPatch, HookContext>({})

// Allow querying on any field from the main schema
// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility
const shopsQueryProperties = Type.Object({
  ...Type.Pick(shopsSchema, ['_id', 'org', 'plans', 'coverages', 'market', 'person', 'plan', 'enrollment', 'createdBy', 'updatedBy']).properties
}, { additionalProperties: true })
export const shopsQuerySchema = queryWrapper(shopsQueryProperties)
export type ShopsQuery = Static<typeof shopsQuerySchema>
export const shopsQueryValidator = getValidator(shopsQuerySchema, queryValidator)
export const shopsQueryResolver = resolve<ShopsQuery, HookContext>({})
