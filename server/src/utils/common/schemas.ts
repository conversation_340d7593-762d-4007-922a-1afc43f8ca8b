import {FromSchema, ObjectIdSchema} from "@feathersjs/schema";
import {AnyObj} from 'feathers-ucan';
import {stateIds} from './states.js';
import {Type} from '@feathersjs/typebox';

export const nullable = {
    'string': {anyOf: [{type: 'string'}, {type: 'null'}]},
    'boolean': {anyOf: [{type: 'boolean'}, {type: 'null'}]}
} as const;
export const isNullable = (type:any) => {
    return {anyOf: [{ type: 'null' }, type]} as const
}
export type NullableString = FromSchema<typeof nullable.string>

export * from './states.js';
export const rRuleSchema = {
    type: 'object',
    required: ['dtstart'],
    properties: {
        //https://icalendar.org/rrule-tool.html
        freq: {
            type: 'string'
            // enum: ['YEARLY', 'MONTHLY', 'WEEKLY', 'DAILY', 'HOURLY', 'MINUTELY', 'SECONDLY'],
        },
        dtstart: {type: 'string'},
        until: {type: 'string'},
        interval: {type: 'number'},
        count: {type: 'number'},
        bymonth: {type: 'array', items: {type: 'number'}}, //month of an annual recurrance
        byweekday: {type: 'array', items: {type: 'string'}}, //ex [TU,TH] first & last sunday [1SU, -1SU]
        byhour: {type: 'array', items: {type: 'number'}},
        byminute: {type: 'array', items: {type: 'number'}},
        bysecond: {type: 'array', items: {type: 'number'}},
        bymonthday: {type: 'array', items: {type: 'number'}}, // 1 to 31
        byyearday: {type: 'array', items: {type: 'number'}}, //1 to 365
        byweekno: {type: 'array', items: {type: 'number'}}, //1 to 52
        bysetpos: {type: 'array', items: {type: 'number'}}, //The third instance into the month of one of Tuesday, Wednesday, or Thursday, for the next 3 months:
        wkst: {type: 'string'},// enum: ['MO', 'TU', 'WE', 'TH', 'FR', 'SA', 'SU'], //day of the week to start weekly recurrance
        tzid: {type: 'string'},
    }
} as const;

export const mandate = {
    type: 'object',
    properties: {
        acceptedAt: {},
        ip: { type: 'string' },
        ua: { type: 'string' },
        copy: { type: 'string' },
        fingerprint: ObjectIdSchema(),
        login: ObjectIdSchema(),
        email: { type: 'string' },
        phone: { type: 'string' },
        signature: { type: 'string'}
    }
} as const;

export type Rrule = FromSchema<typeof rRuleSchema>

// export const dateField = {type: 'string'};
export const phoneSchema = {
    type: 'object',
    additionalProperties: true,
    properties: {
        number: {
            type: 'object',
            properties: {
                input: {type: 'string'},
                international: {type: 'string'},
                national: {type: 'string'},
                e164: {type: 'string'},
                rfc3966: {type: 'string'},
                significant: {type: 'string'},
            }
        },
        regionCode: {type: 'string'},
        valid: {type: 'boolean'},
        possible: {type: 'boolean'},
        possibility: {type: 'string'},
        countryCode: {type: 'number'},
        canBeInternationallyDialled: {type: 'boolean'},
        typeIsMobile: {type: 'boolean'},
        typeIsFixedLine: {type: 'boolean'}
    }
} as const;

export type Phone = FromSchema<typeof phoneSchema>

export const geoJsonFeature = {
    type: 'object',
    additionalProperties: true,
    properties: {
        name: {type: 'string'},
        type: {type: 'string', enum: ['Feature']},//enum: ['Feature']
        geometry: {
            type: 'object',
            additionalProperties: true,
            properties: {
                type: {type: 'string'},//enum: ['Point', 'Polygon', 'MultiPoint], default: 'Point'}
                coordinates: {type: 'array', items: {}}
            }
        },
        properties: {type: 'object'},
        addresses: {type: 'array', items: {}},
    }
} as const
export type GeoJsonFeature = FromSchema<typeof geoJsonFeature>
export const geoJsonSchema = {
    type: 'object',
    additionalProperties: true,
    properties: {
        type: {type: 'string'},// default: 'FeatureCollection' },
        allowFeatures: {type: 'array', items: {type: 'string'}},// enum: ['Point', 'MultiPoint', 'Polygon']}
        features: {type: 'array', items: {type: 'object', properties: geoJsonFeature.properties}}
    }
} as const
export type GeoJsonSchema = FromSchema<typeof geoJsonSchema>

export const imageSchema = {
    // _id: MakeRef(fileUploaderSchema),
    type: 'object',
    additionalProperties: true,
    properties: {
        uploadId: ObjectIdSchema(),
        fileId: {type: 'string'},
        storage: {type: 'string'},
        appPath: nullable.boolean,
        info: {
            type: 'object',
            properties: {
                name: {type: 'string'},
                size: {type: 'number'},
                type: {type: 'string'},
                lastModifiedDate: {},

            }
        },
        subPath: isNullable({ type: 'array', items: {type: 'string'}}),
        url: {type: 'string'}
    }
} as const;

export const videoSchema = {
    type: 'object',
    properties: {
        ...imageSchema.properties,
        title: { type: 'string' },
        author_name: { type: 'string' },
        author_url: { type: 'string' },
        type: { type: 'string' },
        height: {},
        width: {},
        version: {},
        provider_name: { type: 'string' },
        provider_url: { type: 'string' },
        thumbnail_height: { type: 'number' },
        thumbnail_width: { type: 'number' },
        thumbnail_url: { type: 'string' }
    }
} as const

export type Image = FromSchema<typeof imageSchema>

export const updatesSchema = {
    type: 'object',
    additionalProperties: true,
    properties: {
        did: {anyOf: [{type: 'string'}, {type: 'null'}]},
        login: {anyOf: [ObjectIdSchema(), {type: 'null'}, {type: 'string'}]},
        fingerprint: {anyOf: [ObjectIdSchema(), {type: 'null'}, {type: 'string'}]},
        origin: {anyOf: [{type: 'string'}, {type: 'null'}]},
        longtail: {anyOf: [{type: 'string'}, {type: 'null'}]},
        at: {}
    }
} as const;
type Updates = FromSchema<typeof updatesSchema>

const updatedByHistoryEntry = {
    type: 'object',
    additionalProperties: true,
    properties: {...updatesSchema.properties, updatedAt: {}}
} as const
const updatedByHistory = {
    type: 'array', items: updatedByHistoryEntry
} as const

export const commonFields = {
    type: 'object', additionalProperties: true,
    properties: {
        env: ObjectIdSchema(),
        host: ObjectIdSchema(),
        ref: ObjectIdSchema(),
        changeLog: ObjectIdSchema(),
        editMap: {type: 'object'},
        deleted: {type: 'boolean'},
        session_fp: { type: 'string' },
        deletedAt: {},
        updatedAt: {},
        createdAt: {},
        createdBy: {
            type: 'object',
            additionalProperties: true,
            properties: {...updatesSchema.properties}
        },
        updatedBy: {
            type: 'object',
            additionalProperties: true,
            properties: {...updatesSchema.properties},
        },
        updatedByHistory
    }
} as const;

export const existsQuery = () => {
    return {path: '$exists', type: {type: 'object', properties: { $exists: { type: 'boolean' }}}} as const;
}
export const operatorQuery = (propType:any, arr:Array<{path:string, type:any}>) => {
    const anyOf:any = [propType];
    for(const item of arr){
        anyOf.push({ type: 'object', properties: { [item.path]: item.type }})
    }
    return {anyOf} as const;
}

export const commonQueries = {
    type: 'object',
    additionalProperties: true,
    properties: {
        '$select': {},
        // '$or': {},
        '$regex': {},
        '$text': {},
        '$options': {},
        '$search': {},
        '$elemMatch': {},
        '_limit_to': {},
        'updatedBy.login': ObjectIdSchema(),
        'createdBy.login': ObjectIdSchema()
    }
} as const;

const flatProperties = (props:AnyObj):AnyObj => {
    const obj = {};
    const subObj = {};
    for(const k in props){
        obj[k] = props[k];
        if(props[k].type === 'object' && props[k].properties){
            subObj[k] = flatProperties(props[k].properties);
        }
    }
    for(const k in subObj){
        for(const subK in subObj[k]){
            obj[`${k}.${subK}`] = subObj[k][subK];
        }
    }
    return obj;
}

export const commonPatch = (properties: object, push?:PushPull): AnyObj => ({
    type: 'object',
    additionalProperties: false,
    properties: {
        '$unset': {},
        '$set': {
            type: 'object',
            additionalProperties: true,
            properties: flatProperties(properties)
        },
        $push: addToSet(push || [])
    }
}) as const

export const addressSchema = {
    type: 'object',
    additionalProperties: true,
    properties: {
        id: { type: 'string' },
        address1: {type: 'string'},
        address2: {type: 'string'},
        formatted: {type: 'string'},
        postal: {type: 'string'},
        city: {type: 'string'},
        region: {type: 'string'},
        country: {type: 'string'},
        latitude: {type: 'number'},
        longitude: {type: 'number'},
        googleAddress: {type: 'object'},
        name: {type: 'string'},
        tags: {type: 'object'},
        type: {type: 'object'},
    }
} as const;

export const commonFieldsKeys = Object.keys(commonFields);


type PushPull = Array<{ path: string, type: object }>
export const addToSet = (opts: PushPull, config?:any) => {
    const properties = {};
    [{ path: 'updatedByHistory', type: updatedByHistoryEntry }, ...opts].forEach(opt => properties[opt.path] = {
        anyOf: [opt.type, {
            type: 'object',
            additionalProperties: true,
            properties: {
                $position: {type: 'number'},
                $each: {type: 'array', items: opt.type},
                value: opt.type
            }
        }]
    })
    return ({
        type: 'object',
        ...config,
        properties
    }) as const
}

export const pull = (opts: Array<{ path: string, type: object }>) => {
    const properties = {};
    for (const opt of opts) {
        properties[opt.path] = opt.type
    }
    return ({
        type: 'object',
        additionalProperties: true,
        properties
    }) as const;
}

export const exists = (paths, properties) => {
    const props = {};
    for (const path of paths) {
        props[path] = {anyOf: [properties[path], {type: 'object', properties: {$exists: {type: 'boolean'}}}]}
    }
    return props;
}



export const idListQuerySchema = {
    anyOf: [ObjectIdSchema(), {type: 'array', items: ObjectIdSchema()}]
} as const

export const defResolver = (def: any, falseyVals?: Array<any>) => {
    return async (val) => {
        if (val || (falseyVals || []).includes(val)) return val;
        return def;
    }
}

export const isEmail = (str) => {
    return /^(([^<>()[\]\\.,;:\s@']+(\.[^<>()[\]\\.,;:\s@']+)*)|('.+'))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,24}))$/.test(str);
}

type EmailHandlerOptions = { throw?: boolean }
export const emailHandler = (options?: EmailHandlerOptions) => {
    return async (val) => {
        if (val) {
            if (!isEmail(val) && options?.throw) throw new Error(`Invalid email - ${val}`)
            else return val.toLowerCase().trim();
        }
        return val
    }
}
type TrimHandlerOptions = { lowercase?: boolean }
export const trimHandler = ({lowercase}: TrimHandlerOptions) => {
    return async (val) => {
        if (lowercase) val = val?.toLowerCase();
        if (val) return val.trim();
        return val;
    }
}

export const emailMap = (options?: EmailHandlerOptions) => {
    return async (val) => {
        if (val) return await Promise.all(val.map(a => emailHandler(options)(a)))
        return val;
    }
}

const taxItem = {
    type: 'object',
    properties: {
        name: {type: 'string'},
        type: {type: 'string', enum: ['percent', 'flat']},
        rate: {type: 'number'},
        notes: {type: 'string'}
    }
} as const;

export const taxSchema = {
    type: 'object',
    properties: {
        automateTaxes: {type: 'boolean'},
        taxExempt: {type: 'boolean'},
        origin: {type: 'boolean'},
        taxOverrides: {type: 'array', items: taxItem},
        taxes: {
            type: 'array', items: {
                type: 'object',
                properties: {
                    name: {type: 'string'},
                    isDefault: {type: 'boolean'},
                    origin: {type: 'boolean'},
                    areaId: {type: 'string'},
                    states: {type: 'array', items: {type: 'string', enum: stateIds}},
                    postal_codes: {type: 'array', items: {type: 'string'}},
                    cities: {type: 'array', items: {type: 'string'}},
                    taxes: {type: 'array', items: taxItem}
                }
            }
        }
    }
} as const
